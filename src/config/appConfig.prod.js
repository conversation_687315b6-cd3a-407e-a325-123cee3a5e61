const appConfig = {
    "api": {
        "http": {
            "coreService": {
                "v1": {
                    "url": "https://dev.core-service.tgy.cryptobrew.tech",
                    "requestTimeout": 1000,
                    "reconnectInterval": 10000,
                    "maxReconnectAttempts": "5",
                    "retryPolicy": {"maxAttempts": 3, "backoff": 2000 },
                    "paths": {
                        "auth": {
                            "path": "/api/auth",
                            "caching": {
                                "status": "enabled",
                                "ttl": 100
                            }
                        },
                    }
                }
            },
            "configService": {
                "v1": 
                {
                    "url": "https://config.thegameyou.com",
                    "requestTimeout": 1000,
                    "reconnectInterval": 10000,
                    "maxReconnectAttempts": "5",
                    "retryPolicy": {"maxAttempts": 3, "backoff": 2000 },
                    "paths": {
                        "config": {
                            "path": "/api/config/all",
                            "caching": {
                                "status": "enabled",
                                "ttl": 100
                            }
                        },
                    }
                }
            }
        },
        "websocket": {
            "centrifugo": {
                "url": "wss://dev.centrifugo.cryptobrew.tech/connection/websocket",
                "requestTimeout": 1000,
                "reconnectInterval": 10000,
                "maxReconnectAttempts": "5",
                "retryPolicy": {"maxAttempts": 3, "backoff": 2000 },
                "debug": false
            }
        }
    },
  "stripeConfig": {
    "apiKey": ""
  },
  "googleSignInConfig": {
    "webClientId": "372063930446-0lejfet7afv72l3kcssmbjacveqip1f4.apps.googleusercontent.com",
    "scopes": [
      "https://www.googleapis.com/auth/calendar.readonly",
      "https://www.googleapis.com/auth/calendar.events"
    ]
  },
  "sentryConfig": {
    "dsn": "https://<EMAIL>/4509949660889088",
    "secret": "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"
  },
  "logLevel": "debug | info | warn | error",
  "featureFlags": [
    "enableAnalytics",
    "enablePushNotifications",
    "enableOfflineMode",
    "enableRealTime",
    "enableSubscription",
    "enableCalendarSync",
    "enableAbTesting",
    "enableNetworkAwareness",
    "debugCentrifugo",
    "mockApiResponses"
  ],
  "chatHistoryLimit": 10,
  "notificationHistoryLimit": 10,
  "appTheme": {
    "animations": {
      "short": 200,
      "medium": 400,
      "long": 600,
      "xpAnimation": 1000,
      "levelUpAnimation": 2000
    },
    "colors": {
      "primary": "#6366F1",
      "secondary": "#8B5CF6",
      "success": "#10B981",
      "warning": "#F59E0B",
      "error": "#EF4444",
      "info": "#3B82F6",
      "light": {
        "background": "#FFFFFF",
        "surface": "#F9FAFB",
        "textPrimary": "#111827",
        "textSecondary": "#6B7280",
        "border": "#E5E7EB"
      },
      "dark": {
        "background": "#111827",
        "surface": "#1F2937",
        "textPrimary": "#F9FAFB",
        "textSecondary": "#9CA3AF",
        "border": "#374151"
      }
    }
  },
};

export default appConfig;