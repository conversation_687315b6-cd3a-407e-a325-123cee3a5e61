import React, { useEffect } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

import {
  NavigationContainer,
  DefaultTheme,
  DarkTheme,
} from '@react-navigation/native';

import { RootStackParamList, MainTabParamList, User } from '../types';
import ObjectivesScreen from '../screens/objectives/ObjectivesScreen';
import { useState } from 'react';
import { useAppStore } from '../stores/useAppStore';
import LoadingScreen from '../screens/common/LoadingScreen';
import { StatusBar } from 'react-native';
import AuthNavigator from './AuthNavigator';
import { CallbackOrObserver, FirebaseAuthTypes, getAuth, onAuthStateChanged } from '@react-native-firebase/auth';

const RootStack = createNativeStackNavigator<RootStackParamList>();
const MainTab = createBottomTabNavigator<MainTabParamList>();

const MainTabNavigator: React.FC = () => {
    return (
        <>
            <MainTab.Navigator
                screenOptions={
                    {
                        headerShown: false,
                        tabBarShowLabel: false,
                        tabBarStyle: {
                            backgroundColor: '#fff',
                            borderTopWidth: 0,
                            elevation: 0,
                        },
                    }
                }
                >
                <MainTab.Screen
                    name="Objectives"
                    component={ObjectivesScreen}
                    options={{
                        title: "Objectives"
                    }}
                />
            </MainTab.Navigator>
        </>
    )
}

const AppNavigator: React.FC = () => {
    const [isInitializing, setIsInitializing] = useState(true);
    const [initializationError, setInitializationError] = useState<string | null>(
        null,
    );

    const { auth, user, ui, setUser, setAuthenticated, setAuthLoading } = useAppStore();

    const handleAuthStateChanged : CallbackOrObserver<FirebaseAuthTypes.AuthListenerCallback> = (user : FirebaseAuthTypes.User | null) => {
        if(!user?.uid)
            return;
        setAuthenticated(true);
        setAuthLoading(false);
        const appUser : User | null = user ? {
            id: user.uid,
            firebaseUid: user.uid,
            email: user.email || '',
            displayName: user.displayName || '',
            profilePictureUrl: user.photoURL || '',
            alias: user.displayName || '',
            timezone: 'UTC',
            membership: 'FREE',
            preferences: {
                selectedGameMaster: 'default',
                language: 'en',
            },
            createdAt: new Date(),
            updatedAt: new Date(),
            isOnboarded: false,
        } : null;
        setUser(appUser);

    }

    useEffect(() => {
        setAuthLoading(true);
        const unsubscribe = onAuthStateChanged(getAuth(), handleAuthStateChanged);
        return () => unsubscribe();
    }, []);

    useEffect(() => {
        const initializeServices = async () => {
           //TOOD: Initialize centrifugo
           //TODO: Get Membership, preferences, timezone, isOnboarded etc,
           setIsInitializing(false);
        };

        initializeServices();

    }, []);

    if (isInitializing) {
        return <LoadingScreen message="Initializing app services..." />;
    }

    return (
        <NavigationContainer theme={DarkTheme}>
            <StatusBar 
                barStyle={"dark-content"}
                backgroundColor={DarkTheme.colors.card}
            />
            <RootStack.Navigator
                screenOptions={{
                    headerShown: false,
                    animation: 'slide_from_right',
                }}
            >
                {!auth.isAuthenticated ? (
                    <RootStack.Screen
                        name="Auth"
                        component={AuthNavigator}
                        options={{
                            animationTypeForReplace: "push",
                        }}
                    />
                ) : (
                <RootStack.Screen
                    name="Main"
                    component={MainTabNavigator}
                    options={{
                        animationTypeForReplace: "push",
                    }}
                    />
                )}
            </RootStack.Navigator>
        </NavigationContainer>
    )


}

export default AppNavigator;